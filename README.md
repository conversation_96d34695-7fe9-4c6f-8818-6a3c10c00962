# ANKATECH Backend

Backend application for ANKATECH project.

## Getting Started

### Prerequisites

- Node.js (v16 or higher)
- npm or yarn
- PostgreSQL database

### Installation

1. Clone the repository
2. Install dependencies:
   ```bash
   npm install
   ```

3. Copy environment variables:
   ```bash
   cp .env.example .env
   ```

4. Configure your database connection in `.env`

5. Run database migrations:
   ```bash
   npx prisma migrate dev
   ```

6. Start the development server:
   ```bash
   npm run dev
   ```

## Project Structure

```
backend/
├─ docker-compose.yml
├─ package.json
├─ tsconfig.json
├─ .eslintrc.json
├─ .gitignore
├─ .env.example
├─ README.md
├─ prisma/
│  └─ schema.prisma
└─ src/
   ├─ index.ts
   ├─ libs/
   │  └─ prisma.ts
   ├─ routes/
   │  ├─ auth.ts        # (será criado no Dia 2)
   │  └─ clients.ts     # (será criado no Dia 2)
   └─ services/         # (será usado a partir do Dia 3)
```

## Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run start` - Start production server
- `npm run lint` - Run ESLint
- `npm test` - Run tests

## Technologies Used

- Node.js
- TypeScript
- Fastify
- Prisma
- PostgreSQL

## License

This project is private and confidential.
