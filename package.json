{"name": "ankatech_backend", "version": "0.1.0", "private": true, "type": "module", "scripts": {"dev": "ts-node --loader ts-node/esm src/index.ts", "build": "tsc", "start": "node dist/index.js", "prisma:migrate": "prisma migrate dev", "test": "jest --runInBand --detect<PERSON><PERSON>Handles", "lint": "eslint . --ext .ts"}, "dependencies": {"fastify": "4", "@fastify/swagger": "^8", "@prisma/client": "^5", "@fastify/jwt": "^8", "zod": "^4", "@fastify/cors": "^8"}, "devDependencies": {"typescript": "^5", "ts-node-dev": "^2", "prisma": "^5", "jest": "^29", "ts-jest": "^29", "supertest": "^6", "@types/jest": "^29", "@types/supertest": "^2", "eslint": "^8", "eslint-config-prettier": "^8"}}